class BannerClockWidget {
  constructor() {
    this.bannerClock = document.getElementById('banner-clock');
    if (this.bannerClock) {
      this.init();
    }
  }

  init() {
    console.log('🚀 横幅时钟组件独立初始化');
    this.debugLayout();
    this.updateTime();
    this.updateWeather();
    this.setupLocationClick();
    this.setupThemeListener();
    this.subscribeToWeatherUpdates();

    // 每秒更新时间
    setInterval(() => this.updateTime(), 1000);
  }

  // 订阅天气数据更新
  subscribeToWeatherUpdates() {
    if (window.weatherManager) {
      this.weatherSubscription = (data) => {
        console.log('🌤️ 横幅时钟收到天气数据更新:', data);
        this.updateWeatherDisplay(data);
      };

      window.weatherManager.subscribe(this.weatherSubscription);
      console.log('📡 横幅时钟已订阅天气数据');
    } else {
      console.warn('⚠️ 天气管理器未找到，横幅时钟将等待');
      // 等待天气管理器可用
      setTimeout(() => {
        if (window.weatherManager) {
          this.subscribeToWeatherUpdates();
        }
      }, 1000);
    }
  }

  // 取消订阅
  unsubscribeFromWeatherUpdates() {
    if (window.weatherManager && this.weatherSubscription) {
      window.weatherManager.unsubscribe(this.weatherSubscription);
      console.log('📡 横幅时钟已取消订阅天气数据');
    }
  }

  // 设置位置点击事件
  setupLocationClick() {
    const locationEl = this.bannerClock?.querySelector('.banner-clock-location');
    if (locationEl) {
      locationEl.addEventListener('click', () => {
        console.log('📍 点击位置信息');
        if (window.weatherManager) {
          window.weatherManager.showLocationSelector();
        } else {
          console.warn('⚠️ 天气管理器未找到');
        }
      });

      // 添加点击样式提示
      locationEl.style.cursor = 'pointer';
      locationEl.title = '点击选择城市';
    }
  }

  // 设置主题监听器
  setupThemeListener() {
    console.log('🎨 设置主题监听器');

    // 监听主题变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          const theme = document.documentElement.getAttribute('data-theme');
          console.log('🎨 主题变化:', theme);
          this.updateTheme(theme);
        }
      });
    });

    // 开始观察
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    // 初始化主题
    const currentTheme = document.documentElement.getAttribute('data-theme');
    this.updateTheme(currentTheme);
  }

  // 更新主题
  updateTheme(theme) {
    if (!this.bannerClock) return;

    console.log('🎨 更新横幅时钟主题:', theme);

    // 强制触发CSS重新计算
    this.bannerClock.style.display = 'none';
    this.bannerClock.offsetHeight; // 触发重排
    this.bannerClock.style.display = 'block';

    // 添加主题变化动画
    this.bannerClock.style.transition = 'all 0.3s ease';
  }

  // 调试布局信息
  debugLayout() {
    if (this.bannerClock) {
      const rect = this.bannerClock.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(this.bannerClock);
      console.log('🔍 横幅时钟布局调试信息:');
      console.log('- 容器尺寸:', rect.width + 'x' + rect.height);
      console.log('- CSS宽度:', computedStyle.width);
      console.log('- CSS高度:', computedStyle.height);
      console.log('- 内边距:', computedStyle.padding);

      const weatherEl = this.bannerClock.querySelector('.banner-clock-weather');
      if (weatherEl) {
        const weatherRect = weatherEl.getBoundingClientRect();
        const weatherStyle = window.getComputedStyle(weatherEl);
        console.log('- 天气容器尺寸:', weatherRect.width + 'x' + weatherRect.height);
        console.log('- 天气容器display:', weatherStyle.display);
        console.log('- 天气容器flex-direction:', weatherStyle.flexDirection);
      }
    }
  }

  updateTime() {
    const now = new Date();
    const timeEl = this.bannerClock?.querySelector('.banner-clock-time');
    const dateEl = this.bannerClock?.querySelector('.banner-clock-date');

    if (timeEl) {
      const time = now.toLocaleTimeString('zh-CN', { hour12: false });
      timeEl.textContent = time;
    }

    if (dateEl) {
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      const weekday = weekdays[now.getDay()];
      dateEl.textContent = `${month}月${day}日${weekday}`;
    }
  }

  updateWeather() {
    const weatherEl = this.bannerClock?.querySelector('.banner-clock-weather');
    const locationEl = this.bannerClock?.querySelector('.banner-clock-location span');
    
    if (weatherEl) {
      weatherEl.innerHTML = `
        <div class="banner-clock-weather-item">
          <i class="fas fa-cloud-sun"></i>
          <span>获取中...</span>
        </div>
        <div class="banner-clock-weather-item">
          <i class="fas fa-tint"></i>
          <span>--</span>
        </div>
      `;
    }
    
    if (locationEl) {
      locationEl.textContent = '获取位置中...';
    }
  }

  updateWeatherDisplay(data) {
    const weatherEl = this.bannerClock?.querySelector('.banner-clock-weather');
    const locationEl = this.bannerClock?.querySelector('.banner-clock-location span');

    console.log('🌤️ 横幅时钟更新天气显示，数据:', data);

    if (weatherEl && data && data.weather) {
      // 正确访问嵌套的天气数据
      const weatherInfo = data.weather;
      const weatherText = weatherInfo.weather || '晴';
      const temperature = weatherInfo.temperature || '--';
      const humidity = weatherInfo.humidity || '--';

      const weatherIcon = this.getWeatherIcon(weatherText);

      weatherEl.innerHTML = `
        <div class="banner-clock-weather-item">
          <i class="${weatherIcon}"></i>
          <span>${weatherText} ${temperature}°C</span>
        </div>
        <div class="banner-clock-weather-item">
          <i class="fas fa-tint"></i>
          <span>${humidity}%</span>
        </div>
      `;

      console.log('✅ 横幅时钟天气显示已更新:', { weatherText, temperature, humidity });
    } else {
      console.warn('⚠️ 横幅时钟天气数据无效:', data);
    }

    if (locationEl && data?.city) {
      locationEl.textContent = data.city;
      console.log('✅ 横幅时钟位置显示已更新:', data.city);
    }
  }

  // 获取天气图标
  getWeatherIcon(weather) {
    const iconMap = {
      '晴': 'fas fa-sun',
      '多云': 'fas fa-cloud-sun',
      '阴': 'fas fa-cloud',
      '雨': 'fas fa-cloud-rain',
      '雪': 'fas fa-snowflake',
      '雾': 'fas fa-smog',
      '霾': 'fas fa-smog',
      '小雨': 'fas fa-cloud-rain',
      '中雨': 'fas fa-cloud-rain',
      '大雨': 'fas fa-cloud-rain',
      '暴雨': 'fas fa-cloud-rain',
      '雷阵雨': 'fas fa-bolt',
      '小雪': 'fas fa-snowflake',
      '中雪': 'fas fa-snowflake',
      '大雪': 'fas fa-snowflake'
    };
    return iconMap[weather] || 'fas fa-cloud-sun';
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  // 延迟初始化，确保DOM完全加载
  setTimeout(() => {
    if (document.getElementById('banner-clock')) {
      new BannerClockWidget();
    }
  }, 500);
});
