// 时钟组件
class ClockWidget {
  constructor() {
    this.city = null;
    this.weatherData = null;
    this.weatherSubscription = null;
    this.init();
  }

  // 订阅天气数据更新
  subscribeToWeatherUpdates() {
    if (window.weatherManager) {
      this.weatherSubscription = (data) => {
        console.log('🌤️ 侧边栏时钟收到天气数据更新:', data);
        this.city = data.city;
        this.weatherData = data.weather;
        this.updateCityDisplay();
        this.updateWeatherDisplay();
      };

      window.weatherManager.subscribe(this.weatherSubscription);
      console.log('📡 侧边栏时钟已订阅天气数据');
    } else {
      console.warn('⚠️ 天气管理器未找到，侧边栏时钟将使用默认数据');
      this.setDefaultData();
    }
  }

  // 取消订阅
  unsubscribeFromWeatherUpdates() {
    if (window.weatherManager && this.weatherSubscription) {
      window.weatherManager.unsubscribe(this.weatherSubscription);
      console.log('📡 侧边栏时钟已取消订阅天气数据');
    }
  }

  // 设置默认数据
  setDefaultData() {
    this.city = '北京市';
    this.weatherData = {
      weather: '多云',
      temperature: '25',
      humidity: '65'
    };
    this.updateCityDisplay();
    this.updateWeatherDisplay();
  }

  // 更新城市显示
  updateCityDisplay() {
    const cityElement = document.querySelector('.clock-city');
    if (cityElement && this.city) {
      cityElement.textContent = this.city;
      console.log('🏙️ 侧边栏时钟城市显示已更新:', this.city);

      // 添加点击事件，支持手动选择位置
      this.setupLocationClickEvent();
    }
  }

  // 设置位置点击事件
  setupLocationClickEvent() {
    const locationElement = document.querySelector('.clock-location');
    if (locationElement && !locationElement.hasAttribute('data-click-setup')) {
      locationElement.style.cursor = 'pointer';
      locationElement.style.transition = 'opacity 0.3s';
      locationElement.title = '点击更改位置';

      locationElement.addEventListener('click', () => {
        console.log('🖱️ 用户点击侧边栏时钟位置信息');
        this.showLocationSelector();
      });

      // 添加悬停效果
      locationElement.addEventListener('mouseenter', () => {
        locationElement.style.opacity = '0.8';
      });

      locationElement.addEventListener('mouseleave', () => {
        locationElement.style.opacity = '1';
      });

      locationElement.setAttribute('data-click-setup', 'true');
      console.log('✅ 侧边栏时钟位置点击事件已设置');
    }
  }

  // 显示位置选择器
  showLocationSelector() {
    if (window.weatherManager && typeof window.weatherManager.showLocationSelector === 'function') {
      window.weatherManager.showLocationSelector();
    } else {
      console.warn('⚠️ 天气管理器或位置选择器不可用');
    }
  }

  // 清理资源
  destroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
    this.unsubscribeFromWeatherUpdates();
    console.log('🧹 侧边栏时钟组件已清理');
  }









  async init() {
    console.log('🕐 侧边栏时钟组件初始化...');

    // 等待DOM元素可用
    await this.waitForElements();

    // 立即更新时间显示
    this.updateTime();
    this.setupEventListeners();

    // 每秒更新时间
    this.timeInterval = setInterval(() => this.updateTime(), 1000);

    // 等待天气管理器可用（不重复初始化）
    if (window.weatherManager) {
      this.subscribeToWeatherUpdates();
    } else {
      // 如果天气管理器不可用，等待一下再试
      setTimeout(() => {
        if (window.weatherManager) {
          this.subscribeToWeatherUpdates();
        } else {
          this.setDefaultData();
        }
      }, 1000);
    }

    console.log('✅ 侧边栏时钟组件初始化完成');
  }

  // 等待DOM元素可用
  waitForElements() {
    return new Promise((resolve) => {
      const checkElements = () => {
        const timeElement = document.querySelector('.clock-time');
        const dateElement = document.querySelector('.clock-date');
        const weatherElement = document.querySelector('.clock-weather');
        const cityElement = document.querySelector('.clock-city');

        if (timeElement && dateElement && weatherElement && cityElement) {
          console.log('✅ 时钟组件DOM元素已就绪');
          resolve();
        } else {
          console.log('⏳ 等待时钟组件DOM元素...');
          setTimeout(checkElements, 50);
        }
      };
      checkElements();
    });
  }

  // 设置事件监听器
  setupEventListeners() {
    // 设置位置点击事件
    this.setupLocationClickEvent();

    // 确保时钟组件可见
    const clockWidget = document.querySelector('#aside-clock');
    if (clockWidget) {
      clockWidget.style.display = 'block';
      clockWidget.style.visibility = 'visible';
      clockWidget.style.opacity = '1';
    }
  }



  updateTime() {
    const now = new Date();
    const timeElement = document.querySelector('.clock-time');
    const dateElement = document.querySelector('.clock-date');
    
    if (timeElement && dateElement) {
      // 格式化时间
      const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      
      // 格式化日期
      const dateString = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'short'
      });
      
      timeElement.textContent = timeString;
      dateElement.textContent = dateString;
    }
  }







  updateWeatherDisplay(error = false) {
    const weatherElement = document.querySelector('.clock-weather');

    if (weatherElement) {
      if (error || !this.weatherData) {
        weatherElement.innerHTML = `
          <i class="fas fa-cloud"></i>
          <span>多云 25°C</span>
          <i class="fas fa-tint"></i>
          <span>65%</span>
        `;
        console.log('⚠️ 侧边栏时钟使用默认天气显示');
      } else {
        const weather = this.weatherData;
        const weatherIcon = this.getWeatherIcon(weather.weather);

        weatherElement.innerHTML = `
          <i class="${weatherIcon}"></i>
          <span>${weather.weather} ${weather.temperature}°C</span>
          <i class="fas fa-tint"></i>
          <span>${weather.humidity}%</span>
        `;
        console.log('🌤️ 侧边栏时钟天气显示已更新');
      }
    }
  }

  getWeatherIcon(weather) {
    const iconMap = {
      '晴': 'fas fa-sun',
      '多云': 'fas fa-cloud-sun',
      '阴': 'fas fa-cloud',
      '雨': 'fas fa-cloud-rain',
      '雪': 'fas fa-snowflake',
      '雾': 'fas fa-smog',
      '霾': 'fas fa-smog'
    };
    
    for (const key in iconMap) {
      if (weather.includes(key)) {
        return iconMap[key];
      }
    }
    
    return 'fas fa-cloud';
  }
}

// 确保在全局作用域中可用
window.ClockWidget = ClockWidget;